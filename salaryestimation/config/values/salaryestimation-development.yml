Application:
  Environment: "development"
  Name: "salaryestimation"
  ServerName: "central-growth"

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 5m
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
