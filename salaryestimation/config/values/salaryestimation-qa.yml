Application:
  Environment: "qa"
  Name: "salaryestimation"
  ServerName: "central-growth"

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 5m
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/epifimetis/feature_engineering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
